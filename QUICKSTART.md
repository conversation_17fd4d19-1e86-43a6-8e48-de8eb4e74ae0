# 🚀 快速开始指南

欢迎使用AI Comic Generator！这个指南将帮助您快速上手。

## 📋 前置要求

- Python 3.8 或更高版本
- 至少一个AI服务的API密钥：
  - OpenAI API密钥 (推荐)
  - Anthropic API密钥
  - Stability AI API密钥

## 🛠️ 安装步骤

### 1. 自动安装（推荐）

```bash
python setup.py
```

### 2. 手动安装

```bash
# 安装依赖
pip install -r requirements.txt

# 复制配置文件
cp config/config.example.yaml config/config.yaml

# 创建必要目录
mkdir -p output cache logs
```

## ⚙️ 配置

编辑 `config/config.yaml` 文件，添加您的API密钥：

```yaml
api_keys:
  openai: "your-actual-openai-api-key"
  anthropic: "your-actual-anthropic-api-key"
  stability_ai: "your-actual-stability-ai-key"
```

验证配置：

```bash
python main.py --validate-config
```

## 🎨 开始创作

### 命令行方式

```bash
# 基础用法
python main.py "一个关于机器人和人类友谊的温馨故事"

# 自定义参数
python main.py "太空冒险故事" --pages 6 --style "美式漫画" --genre "科幻"

# 仅生成角色设定图
python main.py "可爱的小机器人" --character-only
```

### Web界面方式

```bash
python run_web.py
```

然后在浏览器中访问 http://localhost:8501

## 📖 使用示例

### 生成基础漫画

```python
from src.comic_generator import ComicGenerator

# 初始化
generator = ComicGenerator()

# 生成漫画
comic = generator.generate_comic(
    prompt="一个小女孩和她的魔法猫咪的冒险",
    pages=4,
    style="日式漫画",
    genre="奇幻"
)

# 保存
comic.save("my_comic.pdf")
```

### 生成角色设定图

```python
# 生成角色设定
character_sheet = generator.generate_character_sheet(
    character_description="勇敢的年轻骑士，金色头发，蓝色眼睛",
    style="日式漫画"
)

print("角色设定图已生成:", character_sheet)
```

## 🎭 可用选项

### 绘画风格
- 日式漫画
- 美式漫画
- 水彩风格
- 像素艺术
- 写实风格

### 故事类型
- 冒险
- 喜剧
- 科幻
- 奇幻
- 日常
- 悬疑

### 故事基调
- 轻松
- 严肃
- 幽默
- 温馨
- 紧张

## 📁 输出文件

生成的文件将保存在以下目录：

```
output/
├── images/          # 分镜图像
├── characters/      # 角色设定图
├── backgrounds/     # 背景图
├── pages/          # 完整页面
└── comics/         # 最终漫画PDF
```

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ❌ 请在配置文件中设置正确的API密钥
   ```
   解决：检查 `config/config.yaml` 中的API密钥是否正确

2. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **生成速度慢**
   - 减少页数
   - 使用更快的模型
   - 检查网络连接

4. **图像质量不佳**
   - 优化提示词描述
   - 尝试不同的绘画风格
   - 调整图像生成参数

### 获取帮助

```bash
# 查看命令行帮助
python main.py --help

# 运行测试
python -m pytest tests/

# 查看示例
python examples/example_usage.py
```

## 🎯 最佳实践

1. **提示词优化**
   - 描述要具体详细
   - 包含角色、场景、情节
   - 指定想要的风格和氛围

2. **角色一致性**
   - 先生成角色设定图
   - 在故事中保持角色描述一致
   - 使用相同的风格参数

3. **分镜设计**
   - 考虑故事节奏
   - 平衡对话和动作
   - 注意视觉连贯性

4. **性能优化**
   - 启用缓存功能
   - 批量生成相似内容
   - 合理设置图像分辨率

## 🔄 更新和维护

```bash
# 更新依赖
pip install -r requirements.txt --upgrade

# 清理缓存
rm -rf cache/*

# 备份配置
cp config/config.yaml config/config.backup.yaml
```

## 📞 支持

如果遇到问题：

1. 查看日志文件：`logs/comic_generator.log`
2. 运行测试：`python -m pytest tests/`
3. 查看示例：`examples/example_usage.py`
4. 检查配置：`python main.py --validate-config`

祝您创作愉快！🎨✨
