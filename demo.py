#!/usr/bin/env python3
"""
AI Comic Generator 演示脚本
"""

import sys
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def demo_config_check():
    """演示配置检查"""
    print("🔧 检查配置...")
    
    try:
        from src.utils.config import Config
        
        config = Config("config/config.yaml")
        
        # 检查基本配置
        text_provider = config.get('models.text_generation.provider', 'openai')
        image_provider = config.get('models.image_generation.provider', 'openai')
        
        print(f"✅ 文本模型提供商: {text_provider}")
        print(f"✅ 图像模型提供商: {image_provider}")
        
        # 验证配置
        if config.validate():
            print("✅ 配置验证通过")
            return True
        else:
            print("❌ 配置验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置检查失败: {str(e)}")
        return False


def demo_story_generation():
    """演示故事生成（不调用API）"""
    print("\n📖 演示故事生成结构...")
    
    try:
        from src.generators.story_generator import StoryScript, Page, Panel
        
        # 创建示例故事结构
        panels = [
            Panel(
                panel_number=1,
                scene_description="一个阳光明媚的早晨，小镇广场",
                dialogue=["今天是个好天气！"],
                characters=["小明"],
                action="走向广场",
                mood="愉快"
            ),
            Panel(
                panel_number=2,
                scene_description="广场中央的喷泉",
                dialogue=["哇，这个喷泉好漂亮！"],
                characters=["小明"],
                action="观察喷泉",
                mood="惊喜"
            )
        ]
        
        page = Page(
            page_number=1,
            panels=panels,
            page_summary="小明在广场的美好早晨"
        )
        
        story = StoryScript(
            title="小镇的一天",
            summary="一个关于小镇生活的温馨故事",
            characters=[{
                "name": "小明",
                "age": "12岁",
                "appearance": "黑发，大眼睛，穿着蓝色T恤",
                "personality": "活泼开朗",
                "role": "主角"
            }],
            pages=[page],
            metadata={"genre": "日常", "tone": "温馨"}
        )
        
        print(f"✅ 故事标题: {story.title}")
        print(f"✅ 故事摘要: {story.summary}")
        print(f"✅ 角色数量: {len(story.characters)}")
        print(f"✅ 页数: {len(story.pages)}")
        print(f"✅ 第一页分镜数: {len(story.pages[0].panels)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 故事生成演示失败: {str(e)}")
        return False


def demo_layout_calculation():
    """演示布局计算"""
    print("\n📐 演示布局计算...")
    
    try:
        from src.layout.comic_layout import ComicLayout
        from src.utils.config import Config
        
        config = Config("config/config.yaml")
        layout_engine = ComicLayout(config)
        
        # 测试不同分镜数量的布局
        for panel_count in [1, 2, 3, 4]:
            layouts = layout_engine._calculate_panel_layouts(panel_count)
            print(f"✅ {panel_count}个分镜布局: {len(layouts)}个位置")
            
            for i, layout in enumerate(layouts):
                print(f"   分镜{i+1}: ({layout.x}, {layout.y}) {layout.width}x{layout.height}")
        
        return True
        
    except Exception as e:
        print(f"❌ 布局计算演示失败: {str(e)}")
        return False


def demo_file_structure():
    """演示文件结构"""
    print("\n📁 检查项目文件结构...")
    
    required_files = [
        "README.md",
        "requirements.txt",
        "config/config.example.yaml",
        "src/comic_generator.py",
        "src/models/text_model.py",
        "src/models/image_model.py",
        "src/generators/story_generator.py",
        "src/generators/image_generator.py",
        "src/layout/comic_layout.py",
        "src/utils/config.py",
        "main.py",
        "run_web.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  缺少 {len(missing_files)} 个文件")
        return False
    else:
        print("\n✅ 所有必需文件都存在")
        return True


def demo_directory_creation():
    """演示目录创建"""
    print("\n📂 创建输出目录...")
    
    directories = [
        "output",
        "output/images",
        "output/characters", 
        "output/backgrounds",
        "output/pages",
        "cache",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}/")
    
    return True


def main():
    """运行演示"""
    print("🎨 AI Comic Generator 演示")
    print("=" * 50)
    
    # 设置简单日志
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # 运行各项演示
    demos = [
        ("文件结构检查", demo_file_structure),
        ("目录创建", demo_directory_creation),
        ("配置检查", demo_config_check),
        ("故事生成结构", demo_story_generation),
        ("布局计算", demo_layout_calculation),
    ]
    
    results = []
    for name, demo_func in demos:
        try:
            result = demo_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 演示出错: {str(e)}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 演示结果总结:")
    
    success_count = 0
    for name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {name}")
        if success:
            success_count += 1
    
    print(f"\n🎯 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("\n🎉 所有演示都成功！项目结构完整。")
        print("\n📋 下一步:")
        print("1. 运行 python setup.py 完成安装")
        print("2. 编辑 config/config.yaml 添加API密钥")
        print("3. 运行 python main.py --validate-config 验证配置")
        print("4. 开始创作您的第一个漫画！")
    else:
        print(f"\n⚠️  有 {len(results) - success_count} 个演示失败，请检查项目设置。")
    
    return 0 if success_count == len(results) else 1


if __name__ == "__main__":
    sys.exit(main())
