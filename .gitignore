# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
config/config.yaml
output/
cache/
logs/
*.log

# API keys and secrets
.env
secrets.yaml
api_keys.txt

# Temporary files
temp/
tmp/
*.tmp

# Model files (if using local models)
models/
*.bin
*.safetensors
*.gguf

# Generated content
generated_comics/
test_output/
