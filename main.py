#!/usr/bin/env python3
"""
AI Comic Generator - 命令行界面
"""

import argparse
import logging
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.comic_generator import ComicGenerator
from src.utils.config import Config


def setup_logging(verbose: bool = False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI漫画生成器")
    
    # 基本参数
    parser.add_argument("prompt", help="故事提示词")
    parser.add_argument("-p", "--pages", type=int, default=4, help="页数 (默认: 4)")
    parser.add_argument("-s", "--style", default="日式漫画", help="绘画风格 (默认: 日式漫画)")
    parser.add_argument("-g", "--genre", default="冒险", help="故事类型 (默认: 冒险)")
    parser.add_argument("-o", "--output", default="output/comic.pdf", help="输出文件路径")
    
    # 配置参数
    parser.add_argument("-c", "--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    
    # 功能选项
    parser.add_argument("--character-only", action="store_true", help="仅生成角色设定图")
    parser.add_argument("--validate-config", action="store_true", help="验证配置文件")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    try:
        # 验证配置
        if args.validate_config:
            config = Config(args.config)
            if config.validate():
                print("✅ 配置文件验证通过")
                return 0
            else:
                print("❌ 配置文件验证失败")
                return 1
        
        # 初始化生成器
        logger.info("初始化AI漫画生成器...")
        generator = ComicGenerator(args.config)
        
        # 仅生成角色设定图
        if args.character_only:
            logger.info("生成角色设定图...")
            character_sheet = generator.generate_character_sheet(
                character_description=args.prompt,
                style=args.style
            )
            print(f"✅ 角色设定图已生成: {character_sheet}")
            return 0
        
        # 生成完整漫画
        logger.info(f"开始生成漫画: {args.prompt}")
        comic = generator.generate_comic(
            prompt=args.prompt,
            pages=args.pages,
            style=args.style,
            genre=args.genre
        )
        
        # 保存结果
        logger.info(f"保存漫画到: {args.output}")
        comic.save(args.output)
        
        print(f"✅ 漫画生成完成: {args.output}")
        print(f"📖 标题: {comic.title}")
        print(f"📄 页数: {len(comic.pages)}")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"生成失败: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
