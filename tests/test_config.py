"""
配置管理测试
"""

import pytest
import tempfile
import yaml
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.config import Config


class TestConfig:
    """配置管理测试类"""
    
    def test_config_creation(self):
        """测试配置创建"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            test_config = {
                'api_keys': {
                    'openai': 'test-key'
                },
                'models': {
                    'text_generation': {
                        'provider': 'openai',
                        'model': 'gpt-4'
                    }
                }
            }
            yaml.dump(test_config, f)
            f.flush()
            
            config = Config(f.name)
            assert config.get('api_keys.openai') == 'test-key'
            assert config.get('models.text_generation.provider') == 'openai'
            
            # 清理
            Path(f.name).unlink()
    
    def test_config_get_with_default(self):
        """测试获取配置值（带默认值）"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump({}, f)
            f.flush()
            
            config = Config(f.name)
            assert config.get('nonexistent.key', 'default') == 'default'
            assert config.get('nonexistent.key') is None
            
            # 清理
            Path(f.name).unlink()
    
    def test_config_set(self):
        """测试设置配置值"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump({}, f)
            f.flush()
            
            config = Config(f.name)
            config.set('test.nested.key', 'value')
            assert config.get('test.nested.key') == 'value'
            
            # 清理
            Path(f.name).unlink()
    
    def test_api_key_validation(self):
        """测试API密钥验证"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            test_config = {
                'api_keys': {
                    'openai': 'your-openai-api-key-here'  # 无效的示例密钥
                }
            }
            yaml.dump(test_config, f)
            f.flush()
            
            config = Config(f.name)
            
            with pytest.raises(ValueError):
                config.get_api_key('openai')
            
            # 清理
            Path(f.name).unlink()


if __name__ == "__main__":
    pytest.main([__file__])
