# AI Comic Generator Configuration

# API Keys (请替换为您的实际API密钥)
api_keys:
  openai: "***************************************************"
  anthropic: "your-anthropic-api-key-here"
  stability_ai: "your-stability-ai-key-here"

# 模型配置
models:
  text_generation:
    provider: "openai"  # openai, anthropic, local
    model: "gpt-4"
    max_tokens: 2000
    temperature: 0.7
  
  image_generation:
    provider: "openai"  # openai, stability_ai, local
    model: "dall-e-3"
    size: "1024x1024"
    quality: "standard"

# 生成参数
generation:
  default_pages: 4
  panels_per_page: 4
  max_characters: 5
  
  # 故事生成
  story:
    genre_options: ["冒险", "喜剧", "科幻", "奇幻", "日常", "悬疑"]
    tone_options: ["轻松", "严肃", "幽默", "温馨", "紧张"]
  
  # 图像生成
  image:
    style_options: ["日式漫画", "美式漫画", "水彩风格", "像素艺术", "写实风格"]
    aspect_ratios: ["1:1", "16:9", "4:3", "3:4"]

# 输出设置
output:
  format: "pdf"  # pdf, png, jpg
  resolution: 300  # DPI
  page_size: "A4"
  
  # 布局设置
  layout:
    margin: 20  # 页面边距(像素)
    panel_spacing: 10  # 分镜间距
    text_font: "Arial"
    text_size: 12

# 缓存设置
cache:
  enabled: true
  directory: "cache"
  max_size_mb: 1000

# 日志设置
logging:
  level: "INFO"
  file: "logs/comic_generator.log"
