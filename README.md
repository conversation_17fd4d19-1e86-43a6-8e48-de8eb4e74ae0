# AI Comic Generator

基于大模型一键生成漫画图文内容的智能创作工具

## 功能特性

- 🎨 **智能故事生成**: 基于用户输入自动生成完整的漫画故事情节
- 🖼️ **AI图像生成**: 自动生成漫画角色、场景和分镜画面
- 📖 **自动排版**: 智能漫画页面布局和对话框排版
- 🎭 **角色一致性**: 保持角色在不同场景中的视觉一致性
- 🌐 **多模型支持**: 支持多种大语言模型和图像生成模型

## 技术架构

```
comic-generator/
├── src/
│   ├── models/          # 模型接口和管理
│   ├── generators/      # 内容生成器
│   ├── layout/          # 漫画布局引擎
│   ├── ui/             # 用户界面
│   └── utils/          # 工具函数
├── config/             # 配置文件
├── assets/             # 静态资源
├── output/             # 生成的漫画输出
└── tests/              # 测试文件
```

## 快速开始

### 环境要求

- Python 3.8+
- 支持的AI模型API密钥

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd Comic

# 安装依赖
pip install -r requirements.txt

# 配置API密钥
cp config/config.example.yaml config/config.yaml
# 编辑config.yaml添加您的API密钥
```

### 使用示例

```python
from src.comic_generator import ComicGenerator

# 初始化生成器
generator = ComicGenerator()

# 生成漫画
comic = generator.generate_comic(
    prompt="一个关于机器人和人类友谊的温馨故事",
    pages=4,
    style="日式漫画"
)

# 保存结果
comic.save("output/my_comic.pdf")
```

## 配置说明

在 `config/config.yaml` 中配置：

- AI模型API密钥
- 生成参数
- 输出格式设置
- 样式模板

## 开发计划

- [x] 项目架构设计
- [ ] 文本生成模块
- [ ] 图像生成模块
- [ ] 布局引擎
- [ ] Web界面
- [ ] 批量生成功能

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License
