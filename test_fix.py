#!/usr/bin/env python3
"""
测试修复后的模型初始化
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_openai_model_init():
    """测试OpenAI模型初始化"""
    print("🧪 测试OpenAI模型初始化...")
    
    try:
        from src.models.text_model import OpenAITextModel
        from src.models.image_model import OpenAIImageModel
        
        # 测试文本模型初始化（使用虚假API密钥）
        try:
            text_model = OpenAITextModel(
                api_key="test-key",
                model="gpt-4",
                max_tokens=1000,
                temperature=0.5,
                # 这些参数之前会导致错误
                extra_param="should_be_ignored"
            )
            print("✅ 文本模型初始化成功")
        except TypeError as e:
            if "unexpected keyword argument" in str(e):
                print(f"❌ 文本模型初始化仍有问题: {e}")
                return False
            else:
                # 其他错误（如网络错误）是可以接受的
                print("✅ 文本模型初始化成功（参数过滤正常）")
        
        # 测试图像模型初始化
        try:
            image_model = OpenAIImageModel(
                api_key="test-key",
                model="dall-e-3",
                size="1024x1024",
                quality="standard",
                # 这些参数之前会导致错误
                extra_param="should_be_ignored"
            )
            print("✅ 图像模型初始化成功")
        except TypeError as e:
            if "unexpected keyword argument" in str(e):
                print(f"❌ 图像模型初始化仍有问题: {e}")
                return False
            else:
                # 其他错误（如网络错误）是可以接受的
                print("✅ 图像模型初始化成功（参数过滤正常）")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_config_loading():
    """测试配置加载"""
    print("\n🔧 测试配置加载...")
    
    try:
        from src.utils.config import Config
        
        config = Config("config/config.yaml")
        
        # 测试基本配置获取
        text_provider = config.get('models.text_generation.provider')
        image_provider = config.get('models.image_generation.provider')
        
        print(f"✅ 文本提供商: {text_provider}")
        print(f"✅ 图像提供商: {image_provider}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_model_factory():
    """测试模型工厂"""
    print("\n🏭 测试模型工厂...")
    
    try:
        from src.models.text_model import TextModel
        from src.models.image_model import ImageModel
        from src.utils.config import Config
        
        config = Config("config/config.yaml")
        
        # 测试文本模型工厂
        try:
            text_factory = TextModel(config)
            print("✅ 文本模型工厂创建成功")
        except ValueError as e:
            if "API密钥" in str(e):
                print("⚠️  文本模型工厂需要有效的API密钥")
            else:
                print(f"❌ 文本模型工厂创建失败: {e}")
                return False
        except Exception as e:
            print(f"❌ 文本模型工厂创建失败: {e}")
            return False

        # 测试图像模型工厂
        try:
            image_factory = ImageModel(config)
            print("✅ 图像模型工厂创建成功")
        except ValueError as e:
            if "API密钥" in str(e):
                print("⚠️  图像模型工厂需要有效的API密钥")
            else:
                print(f"❌ 图像模型工厂创建失败: {e}")
                return False
        except Exception as e:
            print(f"❌ 图像模型工厂创建失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模型工厂测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🔧 测试OpenAI客户端初始化修复")
    print("=" * 50)
    
    tests = [
        ("OpenAI模型初始化", test_openai_model_init),
        ("配置加载", test_config_loading),
        ("模型工厂", test_model_factory),
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 测试出错: {str(e)}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    success_count = 0
    for name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {name}")
        if success:
            success_count += 1
    
    print(f"\n🎯 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("\n🎉 所有测试都通过！OpenAI客户端初始化问题已修复。")
        print("\n📋 现在您可以:")
        print("1. 设置有效的API密钥")
        print("2. 运行 python3 main.py --validate-config")
        print("3. 开始生成漫画")
    else:
        print(f"\n⚠️  有 {len(results) - success_count} 个测试失败。")
        print("请检查错误信息并进行相应修复。")
    
    return 0 if success_count == len(results) else 1


if __name__ == "__main__":
    sys.exit(main())
