#!/usr/bin/env python3
"""
启动Web界面
"""

import subprocess
import sys
from pathlib import Path


def main():
    """启动Streamlit应用"""
    app_path = Path(__file__).parent / "src" / "ui" / "streamlit_app.py"
    
    if not app_path.exists():
        print(f"❌ 应用文件不存在: {app_path}")
        return 1
    
    try:
        print("🚀 启动AI漫画生成器Web界面...")
        print("📱 浏览器将自动打开，如果没有请访问: http://localhost:8501")
        
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", str(app_path),
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 感谢使用AI漫画生成器!")
        return 0
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
