#!/usr/bin/env python3
"""
AI Comic Generator 安装脚本
"""

import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """运行命令并处理错误"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {sys.version}")
    return True


def install_dependencies():
    """安装依赖"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "安装Python依赖"
    )


def setup_config():
    """设置配置文件"""
    config_dir = Path("config")
    config_file = config_dir / "config.yaml"
    example_file = config_dir / "config.example.yaml"
    
    if not config_file.exists() and example_file.exists():
        import shutil
        shutil.copy(example_file, config_file)
        print(f"✅ 已创建配置文件: {config_file}")
        print("⚠️  请编辑 config/config.yaml 添加您的API密钥")
        return True
    elif config_file.exists():
        print(f"✅ 配置文件已存在: {config_file}")
        return True
    else:
        print("❌ 配置文件模板不存在")
        return False


def create_directories():
    """创建必要的目录"""
    directories = [
        "output",
        "output/images",
        "output/characters",
        "output/backgrounds",
        "output/pages",
        "cache",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 已创建输出目录")
    return True


def validate_installation():
    """验证安装"""
    try:
        # 尝试导入主要模块
        sys.path.insert(0, "src")
        from src.utils.config import Config
        from src.comic_generator import ComicGenerator
        
        print("✅ 模块导入测试通过")
        
        # 检查配置
        try:
            config = Config("config/config.yaml")
            print("✅ 配置文件加载成功")
        except Exception as e:
            print(f"⚠️  配置文件问题: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def main():
    """主安装流程"""
    print("🎨 AI Comic Generator 安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 安装依赖
    if not install_dependencies():
        print("💡 提示: 如果安装失败，请尝试:")
        print("   pip install --upgrade pip")
        print("   pip install -r requirements.txt")
        return 1
    
    # 设置配置
    if not setup_config():
        return 1
    
    # 创建目录
    if not create_directories():
        return 1
    
    # 验证安装
    if not validate_installation():
        return 1
    
    print("\n🎉 安装完成!")
    print("\n📋 下一步:")
    print("1. 编辑 config/config.yaml 添加您的API密钥")
    print("2. 运行 python main.py --validate-config 验证配置")
    print("3. 运行 python main.py \"您的故事描述\" 生成漫画")
    print("4. 或运行 python run_web.py 启动Web界面")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
