"""
故事生成器
"""

import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass


@dataclass
class Panel:
    """分镜面板"""
    panel_number: int
    scene_description: str
    dialogue: List[str]
    characters: List[str]
    action: str
    mood: str


@dataclass
class Page:
    """漫画页面"""
    page_number: int
    panels: List[Panel]
    page_summary: str


@dataclass
class StoryScript:
    """完整故事脚本"""
    title: str
    summary: str
    characters: List[Dict[str, str]]
    pages: List[Page]
    metadata: Dict[str, Any]


class StoryGenerator:
    """故事生成器"""
    
    def __init__(self, text_model):
        self.text_model = text_model
        self.logger = logging.getLogger(__name__)
    
    def generate_story(
        self,
        prompt: str,
        pages: int = 4,
        genre: str = "冒险",
        tone: str = "轻松",
        **kwargs
    ) -> StoryScript:
        """
        生成完整的漫画故事脚本
        
        Args:
            prompt: 故事提示
            pages: 页数
            genre: 故事类型
            tone: 故事基调
            **kwargs: 其他参数
            
        Returns:
            StoryScript: 完整的故事脚本
        """
        self.logger.info(f"生成故事: {prompt}")
        
        # 1. 生成故事大纲
        story_outline = self._generate_outline(prompt, pages, genre, tone)
        
        # 2. 生成角色设定
        characters = self._generate_characters(story_outline, genre)
        
        # 3. 生成详细分镜脚本
        pages_script = self._generate_pages_script(story_outline, characters, pages)
        
        # 4. 组装完整脚本
        story_script = StoryScript(
            title=story_outline.get('title', '未命名漫画'),
            summary=story_outline.get('summary', ''),
            characters=characters,
            pages=pages_script,
            metadata={
                'genre': genre,
                'tone': tone,
                'original_prompt': prompt
            }
        )
        
        return story_script
    
    def _generate_outline(self, prompt: str, pages: int, genre: str, tone: str) -> Dict[str, Any]:
        """生成故事大纲"""
        outline_prompt = f"""
请根据以下要求创作一个{genre}类型的漫画故事大纲：

用户需求: {prompt}
页数: {pages}页
故事基调: {tone}

请生成一个包含以下内容的故事大纲：
1. 标题
2. 故事摘要（100字以内）
3. 主要情节点（每页一个）
4. 故事主题

请以JSON格式输出：
{{
    "title": "故事标题",
    "summary": "故事摘要",
    "plot_points": ["第1页情节", "第2页情节", ...],
    "theme": "故事主题"
}}
"""
        
        response = self.text_model.generate(outline_prompt)
        
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            # 如果解析失败，返回基础结构
            return {
                "title": "AI生成漫画",
                "summary": prompt,
                "plot_points": [f"第{i+1}页情节" for i in range(pages)],
                "theme": "冒险与成长"
            }
    
    def _generate_characters(self, outline: Dict[str, Any], genre: str) -> List[Dict[str, str]]:
        """生成角色设定"""
        characters_prompt = f"""
根据以下故事大纲，设计主要角色：

故事标题: {outline.get('title', '')}
故事摘要: {outline.get('summary', '')}
故事类型: {genre}

请设计2-4个主要角色，每个角色包含：
1. 姓名
2. 年龄
3. 外貌特征
4. 性格特点
5. 在故事中的作用

请以JSON格式输出：
[
    {{
        "name": "角色姓名",
        "age": "年龄",
        "appearance": "外貌描述",
        "personality": "性格特点",
        "role": "故事作用"
    }},
    ...
]
"""
        
        response = self.text_model.generate(characters_prompt)
        
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            # 返回默认角色
            return [
                {
                    "name": "主角",
                    "age": "16岁",
                    "appearance": "黑发，大眼睛，普通身材",
                    "personality": "勇敢、善良、有正义感",
                    "role": "故事主人公"
                }
            ]
    
    def _generate_pages_script(
        self,
        outline: Dict[str, Any],
        characters: List[Dict[str, str]],
        pages: int
    ) -> List[Page]:
        """生成详细的分镜脚本"""
        pages_script = []
        
        for page_num in range(1, pages + 1):
            page_script = self._generate_page_script(
                outline, characters, page_num, pages
            )
            pages_script.append(page_script)
        
        return pages_script
    
    def _generate_page_script(
        self,
        outline: Dict[str, Any],
        characters: List[Dict[str, str]],
        page_num: int,
        total_pages: int
    ) -> Page:
        """生成单页分镜脚本"""
        plot_points = outline.get('plot_points', [])
        current_plot = plot_points[page_num - 1] if page_num <= len(plot_points) else f"第{page_num}页情节"
        
        character_names = [char['name'] for char in characters]
        
        page_prompt = f"""
为漫画第{page_num}页（共{total_pages}页）创建详细分镜脚本：

故事背景: {outline.get('summary', '')}
当前情节: {current_plot}
可用角色: {', '.join(character_names)}

请创建4个分镜面板，每个面板包含：
1. 场景描述
2. 角色对话
3. 出现的角色
4. 主要动作
5. 情绪氛围

请以JSON格式输出：
{{
    "page_summary": "本页概要",
    "panels": [
        {{
            "panel_number": 1,
            "scene_description": "场景描述",
            "dialogue": ["对话1", "对话2"],
            "characters": ["角色1", "角色2"],
            "action": "主要动作",
            "mood": "情绪氛围"
        }},
        ...
    ]
}}
"""
        
        response = self.text_model.generate(page_prompt)
        
        try:
            page_data = json.loads(response)
            panels = []
            
            for panel_data in page_data.get('panels', []):
                panel = Panel(
                    panel_number=panel_data.get('panel_number', 1),
                    scene_description=panel_data.get('scene_description', ''),
                    dialogue=panel_data.get('dialogue', []),
                    characters=panel_data.get('characters', []),
                    action=panel_data.get('action', ''),
                    mood=panel_data.get('mood', '')
                )
                panels.append(panel)
            
            return Page(
                page_number=page_num,
                panels=panels,
                page_summary=page_data.get('page_summary', f'第{page_num}页')
            )
            
        except (json.JSONDecodeError, KeyError):
            # 返回默认页面
            return self._create_default_page(page_num, current_plot)

    def _create_default_page(self, page_num: int, plot: str) -> Page:
        """创建默认页面"""
        panels = []
        for i in range(4):
            panel = Panel(
                panel_number=i + 1,
                scene_description=f"第{page_num}页第{i+1}个分镜",
                dialogue=[f"对话内容 {i+1}"],
                characters=["主角"],
                action="基本动作",
                mood="平静"
            )
            panels.append(panel)

        return Page(
            page_number=page_num,
            panels=panels,
            page_summary=plot
        )
