"""
图像生成器
"""

import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import hashlib
import json

from .story_generator import Page, Panel


class ImageGenerator:
    """图像生成器"""
    
    def __init__(self, image_model):
        self.image_model = image_model
        self.logger = logging.getLogger(__name__)
        self.character_cache = {}  # 角色一致性缓存
    
    def generate_page_images(
        self,
        page_script: Page,
        style: str = "日式漫画",
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        为页面生成所有分镜图像
        
        Args:
            page_script: 页面脚本
            style: 绘画风格
            **kwargs: 其他参数
            
        Returns:
            图像数据列表
        """
        self.logger.info(f"生成第{page_script.page_number}页图像")
        
        page_images = []
        
        for panel in page_script.panels:
            panel_image = self._generate_panel_image(panel, style, **kwargs)
            page_images.append(panel_image)
        
        return page_images
    
    def _generate_panel_image(
        self,
        panel: Panel,
        style: str,
        **kwargs
    ) -> Dict[str, Any]:
        """生成单个分镜图像"""
        # 构建图像提示词
        prompt = self._build_image_prompt(panel, style)
        
        # 生成图像
        try:
            # 创建输出路径
            output_dir = Path("output/images")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名（基于内容哈希）
            content_hash = hashlib.md5(prompt.encode()).hexdigest()[:8]
            filename = f"panel_{panel.panel_number}_{content_hash}.png"
            output_path = output_dir / filename
            
            # 生成并保存图像
            image_path = self.image_model.generate_and_save(
                prompt=prompt,
                output_path=str(output_path),
                **kwargs
            )
            
            return {
                'panel_number': panel.panel_number,
                'image_path': image_path,
                'prompt': prompt,
                'scene_description': panel.scene_description,
                'characters': panel.characters,
                'dialogue': panel.dialogue,
                'mood': panel.mood
            }
            
        except Exception as e:
            self.logger.error(f"生成分镜图像失败: {str(e)}")
            return {
                'panel_number': panel.panel_number,
                'image_path': None,
                'prompt': prompt,
                'error': str(e),
                'scene_description': panel.scene_description,
                'characters': panel.characters,
                'dialogue': panel.dialogue,
                'mood': panel.mood
            }
    
    def _build_image_prompt(self, panel: Panel, style: str) -> str:
        """构建图像生成提示词"""
        # 基础场景描述
        base_prompt = panel.scene_description
        
        # 添加角色信息
        if panel.characters:
            character_desc = self._get_character_descriptions(panel.characters)
            if character_desc:
                base_prompt += f", 角色: {character_desc}"
        
        # 添加动作和情绪
        if panel.action:
            base_prompt += f", 动作: {panel.action}"
        
        if panel.mood:
            base_prompt += f", 氛围: {panel.mood}"
        
        # 添加风格指令
        style_prompt = self._get_style_prompt(style)
        
        # 组合完整提示词
        full_prompt = f"{base_prompt}, {style_prompt}, 高质量, 详细, 漫画分镜"
        
        return full_prompt
    
    def _get_character_descriptions(self, characters: List[str]) -> str:
        """获取角色描述"""
        descriptions = []
        for char_name in characters:
            if char_name in self.character_cache:
                descriptions.append(self.character_cache[char_name])
            else:
                # 如果没有缓存，使用基本描述
                descriptions.append(char_name)
        
        return ", ".join(descriptions)
    
    def _get_style_prompt(self, style: str) -> str:
        """获取风格提示词"""
        style_prompts = {
            "日式漫画": "anime style, manga style, Japanese comic art",
            "美式漫画": "comic book style, American comic art, superhero style",
            "水彩风格": "watercolor style, soft colors, artistic",
            "像素艺术": "pixel art style, 8-bit, retro gaming",
            "写实风格": "realistic style, photorealistic, detailed"
        }
        
        return style_prompts.get(style, "comic book style")
    
    def generate_character_sheet(
        self,
        description: str,
        style: str = "日式漫画",
        poses: List[str] = None
    ) -> Dict[str, Any]:
        """
        生成角色设定图
        
        Args:
            description: 角色描述
            style: 绘画风格
            poses: 姿势列表
            
        Returns:
            角色设定图数据
        """
        poses = poses or ["正面", "侧面", "背面", "表情变化"]
        
        character_images = []
        style_prompt = self._get_style_prompt(style)
        
        for pose in poses:
            prompt = f"{description}, {pose}, {style_prompt}, 角色设定图, 白色背景"
            
            try:
                # 创建输出路径
                output_dir = Path("output/characters")
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # 生成文件名
                char_hash = hashlib.md5(description.encode()).hexdigest()[:8]
                filename = f"character_{char_hash}_{pose}.png"
                output_path = output_dir / filename
                
                # 生成图像
                image_path = self.image_model.generate_and_save(
                    prompt=prompt,
                    output_path=str(output_path)
                )
                
                character_images.append({
                    'pose': pose,
                    'image_path': image_path,
                    'prompt': prompt
                })
                
            except Exception as e:
                self.logger.error(f"生成角色图像失败 ({pose}): {str(e)}")
                character_images.append({
                    'pose': pose,
                    'image_path': None,
                    'error': str(e)
                })
        
        return {
            'description': description,
            'style': style,
            'images': character_images
        }
    
    def set_character_cache(self, characters: List[Dict[str, str]]) -> None:
        """
        设置角色缓存，用于保持角色一致性
        
        Args:
            characters: 角色列表，包含name和appearance字段
        """
        for char in characters:
            name = char.get('name', '')
            appearance = char.get('appearance', '')
            if name and appearance:
                self.character_cache[name] = appearance
        
        self.logger.info(f"已缓存 {len(self.character_cache)} 个角色")
    
    def generate_background(
        self,
        scene_description: str,
        style: str = "日式漫画",
        **kwargs
    ) -> str:
        """
        生成背景图像
        
        Args:
            scene_description: 场景描述
            style: 绘画风格
            **kwargs: 其他参数
            
        Returns:
            背景图像路径
        """
        style_prompt = self._get_style_prompt(style)
        prompt = f"{scene_description}, {style_prompt}, 背景图, 无人物, 详细环境"
        
        # 创建输出路径
        output_dir = Path("output/backgrounds")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        scene_hash = hashlib.md5(scene_description.encode()).hexdigest()[:8]
        filename = f"background_{scene_hash}.png"
        output_path = output_dir / filename
        
        return self.image_model.generate_and_save(
            prompt=prompt,
            output_path=str(output_path),
            **kwargs
        )
