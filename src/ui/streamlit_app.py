"""
Streamlit Web界面
"""

import streamlit as st
import sys
from pathlib import Path
import logging
import traceback

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.comic_generator import ComicGenerator
from src.utils.config import Config


def setup_page():
    """设置页面配置"""
    st.set_page_config(
        page_title="AI漫画生成器",
        page_icon="🎨",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    st.title("🎨 AI漫画生成器")
    st.markdown("基于大模型一键生成漫画图文内容")


def setup_sidebar():
    """设置侧边栏"""
    st.sidebar.header("⚙️ 生成设置")
    
    # 基本设置
    pages = st.sidebar.slider("页数", min_value=1, max_value=10, value=4)
    
    # 风格选择
    styles = ["日式漫画", "美式漫画", "水彩风格", "像素艺术", "写实风格"]
    style = st.sidebar.selectbox("绘画风格", styles)
    
    # 故事类型
    genres = ["冒险", "喜剧", "科幻", "奇幻", "日常", "悬疑"]
    genre = st.sidebar.selectbox("故事类型", genres)
    
    # 故事基调
    tones = ["轻松", "严肃", "幽默", "温馨", "紧张"]
    tone = st.sidebar.selectbox("故事基调", tones)
    
    return {
        'pages': pages,
        'style': style,
        'genre': genre,
        'tone': tone
    }


def check_config():
    """检查配置"""
    try:
        config = Config("config/config.yaml")
        if not config.validate():
            st.error("❌ 配置文件验证失败，请检查API密钥设置")
            st.info("请在 config/config.yaml 中设置正确的API密钥")
            return False
        return True
    except Exception as e:
        st.error(f"❌ 配置加载失败: {str(e)}")
        return False


def main():
    """主界面"""
    setup_page()
    
    # 检查配置
    if not check_config():
        st.stop()
    
    # 侧边栏设置
    settings = setup_sidebar()
    
    # 主要内容区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📝 故事创作")
        
        # 故事提示输入
        prompt = st.text_area(
            "请描述您想要的漫画故事:",
            placeholder="例如: 一个关于机器人和人类友谊的温馨故事...",
            height=100
        )
        
        # 生成按钮
        col_btn1, col_btn2, col_btn3 = st.columns(3)
        
        with col_btn1:
            generate_comic = st.button("🎨 生成漫画", type="primary", use_container_width=True)
        
        with col_btn2:
            generate_character = st.button("👤 生成角色设定", use_container_width=True)
        
        with col_btn3:
            clear_output = st.button("🗑️ 清空输出", use_container_width=True)
    
    with col2:
        st.header("ℹ️ 使用说明")
        st.markdown("""
        1. **输入故事描述**: 在左侧文本框中描述您想要的漫画故事
        2. **调整设置**: 在侧边栏中选择页数、风格等参数
        3. **生成漫画**: 点击"生成漫画"按钮开始创作
        4. **查看结果**: 生成完成后可以下载PDF文件
        
        **提示**:
        - 描述越详细，生成效果越好
        - 建议先生成角色设定图确认风格
        - 生成过程可能需要几分钟时间
        """)
    
    # 清空输出
    if clear_output:
        if 'generation_results' in st.session_state:
            del st.session_state.generation_results
        st.rerun()
    
    # 生成角色设定图
    if generate_character and prompt:
        with st.spinner("正在生成角色设定图..."):
            try:
                generator = ComicGenerator("config/config.yaml")
                character_sheet = generator.generate_character_sheet(
                    character_description=prompt,
                    style=settings['style']
                )
                
                st.success("✅ 角色设定图生成完成!")
                
                # 显示角色设定图
                st.subheader("👤 角色设定图")
                for image_info in character_sheet['images']:
                    if image_info.get('image_path'):
                        st.image(image_info['image_path'], caption=f"姿势: {image_info['pose']}")
                
            except Exception as e:
                st.error(f"❌ 生成失败: {str(e)}")
                if st.checkbox("显示详细错误信息"):
                    st.code(traceback.format_exc())
    
    # 生成完整漫画
    if generate_comic and prompt:
        with st.spinner("正在生成漫画，请耐心等待..."):
            try:
                # 初始化生成器
                generator = ComicGenerator("config/config.yaml")
                
                # 创建进度条
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                # 生成漫画
                status_text.text("正在生成故事脚本...")
                progress_bar.progress(20)
                
                comic = generator.generate_comic(
                    prompt=prompt,
                    pages=settings['pages'],
                    style=settings['style'],
                    genre=settings['genre'],
                    tone=settings['tone']
                )
                
                progress_bar.progress(100)
                status_text.text("生成完成!")
                
                # 保存结果到session state
                st.session_state.generation_results = {
                    'comic': comic,
                    'settings': settings,
                    'prompt': prompt
                }
                
                st.success("✅ 漫画生成完成!")
                
            except Exception as e:
                st.error(f"❌ 生成失败: {str(e)}")
                if st.checkbox("显示详细错误信息"):
                    st.code(traceback.format_exc())
    
    # 显示生成结果
    if 'generation_results' in st.session_state:
        results = st.session_state.generation_results
        comic = results['comic']
        
        st.header("📖 生成结果")
        
        # 漫画信息
        col_info1, col_info2, col_info3 = st.columns(3)
        with col_info1:
            st.metric("标题", comic.title)
        with col_info2:
            st.metric("页数", len(comic.pages))
        with col_info3:
            st.metric("风格", results['settings']['style'])
        
        # 显示每一页
        for page in comic.pages:
            with st.expander(f"第 {page.page_number} 页 - {page.layout_info.get('page_summary', '')}", expanded=True):
                
                # 显示分镜
                cols = st.columns(2)
                for i, panel in enumerate(page.panels):
                    col_idx = i % 2
                    with cols[col_idx]:
                        st.subheader(f"分镜 {panel['panel_number']}")
                        
                        # 显示图像
                        image_data = panel.get('image_data', {})
                        if image_data.get('image_path'):
                            st.image(image_data['image_path'])
                        
                        # 显示对话
                        if image_data.get('dialogue'):
                            st.write("💬 对话:")
                            for dialogue in image_data['dialogue']:
                                st.write(f"- {dialogue}")
        
        # 下载按钮
        st.download_button(
            label="📥 下载漫画PDF",
            data=b"PDF content placeholder",  # TODO: 实现PDF生成
            file_name=f"{comic.title}.pdf",
            mime="application/pdf"
        )


if __name__ == "__main__":
    main()
