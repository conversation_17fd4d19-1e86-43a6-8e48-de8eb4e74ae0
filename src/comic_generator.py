"""
AI Comic Generator - 主要生成器类
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from .models.text_model import TextModel
from .models.image_model import ImageModel
from .generators.story_generator import StoryGenerator
from .generators.image_generator import ImageGenerator
from .layout.comic_layout import ComicLayout
from .utils.config import Config


@dataclass
class ComicPage:
    """漫画页面数据结构"""
    page_number: int
    panels: List[Dict[str, Any]]
    layout_info: Dict[str, Any]


@dataclass
class Comic:
    """完整漫画数据结构"""
    title: str
    pages: List[ComicPage]
    metadata: Dict[str, Any]
    
    def save(self, output_path: str) -> None:
        """保存漫画到文件"""
        from pathlib import Path
        import logging

        logger = logging.getLogger(__name__)
        output_path = Path(output_path)

        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)

        if output_path.suffix.lower() == '.pdf':
            self._save_as_pdf(output_path)
        else:
            self._save_as_images(output_path)

        logger.info(f"漫画已保存: {output_path}")

    def _save_as_pdf(self, output_path: Path) -> None:
        """保存为PDF格式"""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import A4
            from PIL import Image

            c = canvas.Canvas(str(output_path), pagesize=A4)
            page_width, page_height = A4

            for page in self.pages:
                # 添加标题页
                if page.page_number == 1:
                    c.setFont("Helvetica-Bold", 24)
                    c.drawCentredText(page_width/2, page_height - 100, self.title)

                # 渲染页面内容
                for panel in page.panels:
                    image_data = panel.get('image_data', {})
                    if image_data.get('image_path') and Path(image_data['image_path']).exists():
                        c.drawImage(image_data['image_path'], 50, 50,
                                  width=page_width-100, height=page_height-200)
                        break

                c.showPage()

            c.save()

        except ImportError:
            # 如果没有reportlab，保存为图像
            self._save_as_images(output_path.with_suffix('.png'))

    def _save_as_images(self, output_path: Path) -> None:
        """保存为图像格式"""
        for page in self.pages:
            page_output = output_path.parent / f"{output_path.stem}_page_{page.page_number:03d}.png"

            # 简化实现：保存第一个分镜图像
            for panel in page.panels:
                image_data = panel.get('image_data', {})
                if image_data.get('image_path') and Path(image_data['image_path']).exists():
                    import shutil
                    shutil.copy(image_data['image_path'], page_output)
                    break


class ComicGenerator:
    """AI漫画生成器主类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化漫画生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = Config(config_path)
        self._setup_logging()
        self._initialize_models()
        
    def _setup_logging(self) -> None:
        """设置日志"""
        log_config = self.config.get('logging', {})
        level = getattr(logging, log_config.get('level', 'INFO'))
        
        # 创建logs目录
        log_file = log_config.get('file', 'logs/comic_generator.log')
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def _initialize_models(self) -> None:
        """初始化AI模型"""
        self.text_model = TextModel(self.config)
        self.image_model = ImageModel(self.config)
        self.story_generator = StoryGenerator(self.text_model)
        self.image_generator = ImageGenerator(self.image_model)
        self.layout_engine = ComicLayout(self.config)
        
    def generate_comic(
        self,
        prompt: str,
        pages: Optional[int] = None,
        style: Optional[str] = None,
        genre: Optional[str] = None,
        **kwargs
    ) -> Comic:
        """
        生成完整漫画
        
        Args:
            prompt: 用户输入的故事提示
            pages: 页数（默认使用配置文件设置）
            style: 绘画风格
            genre: 故事类型
            **kwargs: 其他生成参数
            
        Returns:
            Comic: 生成的漫画对象
        """
        self.logger.info(f"开始生成漫画: {prompt}")
        
        # 使用默认值
        pages = pages or self.config.get('generation.default_pages', 4)
        style = style or "日式漫画"
        genre = genre or "冒险"
        
        try:
            # 1. 生成故事大纲和分镜脚本
            self.logger.info("生成故事脚本...")
            story_script = self.story_generator.generate_story(
                prompt=prompt,
                pages=pages,
                genre=genre,
                **kwargs
            )
            
            # 2. 为每个分镜生成图像
            self.logger.info("生成漫画图像...")
            comic_pages = []
            
            for page_num, page_script in enumerate(story_script.pages, 1):
                page_images = self.image_generator.generate_page_images(
                    page_script=page_script,
                    style=style,
                    **kwargs
                )
                
                # 3. 布局和排版
                page_layout = self.layout_engine.create_page_layout(
                    page_script=page_script,
                    images=page_images,
                    page_number=page_num
                )
                
                comic_page = ComicPage(
                    page_number=page_num,
                    panels=page_layout.panels,
                    layout_info=page_layout.metadata
                )
                comic_pages.append(comic_page)
                
                self.logger.info(f"完成第 {page_num} 页")
            
            # 4. 创建最终漫画对象
            comic = Comic(
                title=story_script.title,
                pages=comic_pages,
                metadata={
                    'prompt': prompt,
                    'style': style,
                    'genre': genre,
                    'generation_params': kwargs
                }
            )
            
            self.logger.info("漫画生成完成!")
            return comic
            
        except Exception as e:
            self.logger.error(f"生成漫画时出错: {str(e)}")
            raise
    
    def generate_character_sheet(
        self,
        character_description: str,
        style: str = "日式漫画",
        poses: List[str] = None
    ) -> Dict[str, Any]:
        """
        生成角色设定图
        
        Args:
            character_description: 角色描述
            style: 绘画风格
            poses: 姿势列表
            
        Returns:
            角色设定图数据
        """
        poses = poses or ["正面", "侧面", "背面", "表情变化"]
        
        return self.image_generator.generate_character_sheet(
            description=character_description,
            style=style,
            poses=poses
        )
    
    def get_available_styles(self) -> List[str]:
        """获取可用的绘画风格"""
        return self.config.get('generation.image.style_options', [])
    
    def get_available_genres(self) -> List[str]:
        """获取可用的故事类型"""
        return self.config.get('generation.story.genre_options', [])
