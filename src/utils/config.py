"""
配置管理工具
"""

import yaml
import os
from pathlib import Path
from typing import Any, Dict, Optional
from dotenv import load_dotenv


class Config:
    """配置管理类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化配置
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self._config_data = {}
        self._load_config()
        self._load_env_vars()
    
    def _load_config(self) -> None:
        """加载YAML配置文件"""
        if not self.config_path.exists():
            # 如果配置文件不存在，尝试从示例文件复制
            example_path = self.config_path.parent / "config.example.yaml"
            if example_path.exists():
                import shutil
                shutil.copy(example_path, self.config_path)
                print(f"已从 {example_path} 创建配置文件 {self.config_path}")
                print("请编辑配置文件添加您的API密钥")
            else:
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self._config_data = yaml.safe_load(f) or {}
    
    def _load_env_vars(self) -> None:
        """加载环境变量"""
        load_dotenv()
        
        # 从环境变量覆盖API密钥
        env_mappings = {
            'OPENAI_API_KEY': 'api_keys.openai',
            'ANTHROPIC_API_KEY': 'api_keys.anthropic',
            'STABILITY_AI_KEY': 'api_keys.stability_ai',
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                self.set(config_path, env_value)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，如 'models.text_generation.model'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self) -> None:
        """保存配置到文件"""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self._config_data, f, default_flow_style=False, allow_unicode=True)
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """
        获取API密钥
        
        Args:
            provider: 提供商名称 (openai, anthropic, stability_ai)
            
        Returns:
            API密钥
        """
        key = self.get(f'api_keys.{provider}')
        if not key or key.startswith('your-'):
            raise ValueError(f"请在配置文件中设置 {provider} 的API密钥")
        return key
    
    def validate(self) -> bool:
        """
        验证配置的完整性
        
        Returns:
            配置是否有效
        """
        required_keys = [
            'models.text_generation.provider',
            'models.image_generation.provider',
        ]
        
        for key in required_keys:
            if self.get(key) is None:
                print(f"缺少必需的配置项: {key}")
                return False
        
        # 验证API密钥
        text_provider = self.get('models.text_generation.provider')
        image_provider = self.get('models.image_generation.provider')
        
        try:
            if text_provider != 'local':
                self.get_api_key(text_provider)
            if image_provider != 'local':
                self.get_api_key(image_provider)
        except ValueError as e:
            print(f"API密钥验证失败: {e}")
            return False
        
        return True
