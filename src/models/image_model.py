"""
图像生成模型接口
"""

import logging
import requests
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod
from pathlib import Path
import base64
from io import BytesIO

try:
    import openai
except ImportError:
    openai = None

try:
    from PIL import Image
except ImportError:
    Image = None


class BaseImageModel(ABC):
    """图像模型基类"""
    
    @abstractmethod
    def generate(self, prompt: str, **kwargs) -> Union[str, bytes, Image.Image]:
        """生成图像"""
        pass


class OpenAIImageModel(BaseImageModel):
    """OpenAI图像模型 (DALL-E)"""
    
    def __init__(self, api_key: str, model: str = "dall-e-3", **kwargs):
        if not openai:
            raise ImportError("请安装openai库: pip install openai")

        # 只传递OpenAI客户端支持的参数
        client_kwargs = {}
        if 'base_url' in kwargs:
            client_kwargs['base_url'] = kwargs['base_url']
        if 'organization' in kwargs:
            client_kwargs['organization'] = kwargs['organization']
        if 'timeout' in kwargs:
            client_kwargs['timeout'] = kwargs['timeout']

        self.client = openai.OpenAI(api_key=api_key, **client_kwargs)
        self.model = model
        self.default_params = {
            'size': kwargs.get('size', '1024x1024'),
            'quality': kwargs.get('quality', 'standard'),
            'n': 1,
        }
    
    def generate(self, prompt: str, **kwargs) -> str:
        """生成图像并返回URL"""
        params = {**self.default_params, **kwargs}
        
        response = self.client.images.generate(
            model=self.model,
            prompt=prompt,
            **params
        )
        
        return response.data[0].url
    
    def download_image(self, url: str) -> Image.Image:
        """下载图像"""
        if not Image:
            raise ImportError("请安装Pillow库: pip install Pillow")
        
        response = requests.get(url)
        response.raise_for_status()
        return Image.open(BytesIO(response.content))


class StabilityAIImageModel(BaseImageModel):
    """Stability AI图像模型"""
    
    def __init__(self, api_key: str, model: str = "stable-diffusion-xl-1024-v1-0", **kwargs):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://api.stability.ai/v1/generation"
        self.default_params = {
            'width': kwargs.get('width', 1024),
            'height': kwargs.get('height', 1024),
            'steps': kwargs.get('steps', 30),
            'cfg_scale': kwargs.get('cfg_scale', 7),
        }
    
    def generate(self, prompt: str, **kwargs) -> bytes:
        """生成图像并返回字节数据"""
        params = {**self.default_params, **kwargs}
        
        url = f"{self.base_url}/{self.model}/text-to-image"
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        
        data = {
            "text_prompts": [{"text": prompt}],
            "width": params['width'],
            "height": params['height'],
            "steps": params['steps'],
            "cfg_scale": params['cfg_scale'],
        }
        
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        image_data = base64.b64decode(result['artifacts'][0]['base64'])
        return image_data


class LocalImageModel(BaseImageModel):
    """本地图像模型"""
    
    def __init__(self, model_path: str, **kwargs):
        self.model_path = model_path
        # TODO: 实现本地模型加载
        logging.warning("本地图像模型尚未实现")
    
    def generate(self, prompt: str, **kwargs) -> str:
        """生成图像"""
        # TODO: 实现本地模型推理
        return "placeholder_image_url"


class ImageModel:
    """图像模型工厂类"""
    
    def __init__(self, config):
        self.config = config
        self.model = self._create_model()
    
    def _create_model(self) -> BaseImageModel:
        """根据配置创建模型"""
        provider = self.config.get('models.image_generation.provider')
        model_name = self.config.get('models.image_generation.model')
        
        if provider == 'openai':
            api_key = self.config.get_api_key('openai')
            return OpenAIImageModel(
                api_key=api_key,
                model=model_name,
                size=self.config.get('models.image_generation.size'),
                quality=self.config.get('models.image_generation.quality')
            )
        elif provider == 'stability_ai':
            api_key = self.config.get_api_key('stability_ai')
            return StabilityAIImageModel(
                api_key=api_key,
                model=model_name
            )
        elif provider == 'local':
            return LocalImageModel(model_path=model_name)
        else:
            raise ValueError(f"不支持的图像模型提供商: {provider}")
    
    def generate(self, prompt: str, **kwargs) -> Union[str, bytes, Image.Image]:
        """生成图像"""
        return self.model.generate(prompt, **kwargs)
    
    def generate_and_save(self, prompt: str, output_path: str, **kwargs) -> str:
        """
        生成图像并保存到文件
        
        Args:
            prompt: 图像描述
            output_path: 输出路径
            **kwargs: 其他参数
            
        Returns:
            保存的文件路径
        """
        result = self.generate(prompt, **kwargs)
        
        # 确保输出目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        if isinstance(result, str):  # URL
            if hasattr(self.model, 'download_image'):
                image = self.model.download_image(result)
                image.save(output_path)
            else:
                # 下载图像
                response = requests.get(result)
                response.raise_for_status()
                with open(output_path, 'wb') as f:
                    f.write(response.content)
        elif isinstance(result, bytes):  # 字节数据
            with open(output_path, 'wb') as f:
                f.write(result)
        elif Image and isinstance(result, Image.Image):  # PIL图像
            result.save(output_path)
        else:
            raise ValueError(f"不支持的图像格式: {type(result)}")
        
        return output_path
