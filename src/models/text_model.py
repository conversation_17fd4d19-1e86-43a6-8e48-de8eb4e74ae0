"""
文本生成模型接口
"""

import logging
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod

try:
    import openai
except ImportError:
    openai = None

try:
    import anthropic
except ImportError:
    anthropic = None


class BaseTextModel(ABC):
    """文本模型基类"""
    
    @abstractmethod
    def generate(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        pass


class OpenAITextModel(BaseTextModel):
    """OpenAI文本模型"""
    
    def __init__(self, api_key: str, model: str = "gpt-4", **kwargs):
        if not openai:
            raise ImportError("请安装openai库: pip install openai")
        
        self.client = openai.OpenAI(api_key=api_key)
        self.model = model
        self.default_params = {
            'max_tokens': kwargs.get('max_tokens', 2000),
            'temperature': kwargs.get('temperature', 0.7),
        }
    
    def generate(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        params = {**self.default_params, **kwargs}
        
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            **params
        )
        
        return response.choices[0].message.content


class AnthropicTextModel(BaseTextModel):
    """Anthropic文本模型"""
    
    def __init__(self, api_key: str, model: str = "claude-3-sonnet-20240229", **kwargs):
        if not anthropic:
            raise ImportError("请安装anthropic库: pip install anthropic")
        
        self.client = anthropic.Anthropic(api_key=api_key)
        self.model = model
        self.default_params = {
            'max_tokens': kwargs.get('max_tokens', 2000),
            'temperature': kwargs.get('temperature', 0.7),
        }
    
    def generate(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        params = {**self.default_params, **kwargs}
        
        response = self.client.messages.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            **params
        )
        
        return response.content[0].text


class LocalTextModel(BaseTextModel):
    """本地文本模型（占位符）"""
    
    def __init__(self, model_path: str, **kwargs):
        self.model_path = model_path
        # TODO: 实现本地模型加载
        logging.warning("本地文本模型尚未实现")
    
    def generate(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        # TODO: 实现本地模型推理
        return "本地模型生成的示例文本"


class TextModel:
    """文本模型工厂类"""
    
    def __init__(self, config):
        self.config = config
        self.model = self._create_model()
    
    def _create_model(self) -> BaseTextModel:
        """根据配置创建模型"""
        provider = self.config.get('models.text_generation.provider')
        model_name = self.config.get('models.text_generation.model')
        
        if provider == 'openai':
            api_key = self.config.get_api_key('openai')
            return OpenAITextModel(
                api_key=api_key,
                model=model_name,
                max_tokens=self.config.get('models.text_generation.max_tokens'),
                temperature=self.config.get('models.text_generation.temperature')
            )
        elif provider == 'anthropic':
            api_key = self.config.get_api_key('anthropic')
            return AnthropicTextModel(
                api_key=api_key,
                model=model_name,
                max_tokens=self.config.get('models.text_generation.max_tokens'),
                temperature=self.config.get('models.text_generation.temperature')
            )
        elif provider == 'local':
            return LocalTextModel(model_path=model_name)
        else:
            raise ValueError(f"不支持的文本模型提供商: {provider}")
    
    def generate(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        return self.model.generate(prompt, **kwargs)
    
    def generate_structured(self, prompt: str, schema: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        生成结构化文本
        
        Args:
            prompt: 提示词
            schema: 期望的输出结构
            **kwargs: 其他参数
            
        Returns:
            结构化数据
        """
        # 添加结构化输出指令
        structured_prompt = f"""
{prompt}

请按照以下JSON格式输出结果:
{schema}

确保输出是有效的JSON格式。
"""
        
        response = self.generate(structured_prompt, **kwargs)
        
        # 尝试解析JSON
        import json
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            # 如果解析失败，尝试提取JSON部分
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                raise ValueError("无法解析结构化输出")
