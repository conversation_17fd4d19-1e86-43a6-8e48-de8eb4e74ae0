"""
漫画布局引擎
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path

try:
    from PIL import Image, ImageDraw, ImageFont
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.units import inch
except ImportError:
    Image = ImageDraw = ImageFont = None
    canvas = A4 = inch = None

from ..generators.story_generator import Page


@dataclass
class PanelLayout:
    """分镜布局信息"""
    x: int
    y: int
    width: int
    height: int
    panel_number: int


@dataclass
class PageLayout:
    """页面布局信息"""
    page_number: int
    width: int
    height: int
    panels: List[Dict[str, Any]]
    metadata: Dict[str, Any]


class ComicLayout:
    """漫画布局引擎"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 布局参数
        self.page_width = 2480  # A4 at 300 DPI
        self.page_height = 3508
        self.margin = config.get('output.layout.margin', 20)
        self.panel_spacing = config.get('output.layout.panel_spacing', 10)
        
        # 字体设置
        self.font_name = config.get('output.layout.text_font', 'Arial')
        self.font_size = config.get('output.layout.text_size', 12)
        
        if not Image:
            self.logger.warning("PIL未安装，某些布局功能可能不可用")
    
    def create_page_layout(
        self,
        page_script: Page,
        images: List[Dict[str, Any]],
        page_number: int
    ) -> PageLayout:
        """
        创建页面布局
        
        Args:
            page_script: 页面脚本
            images: 图像数据列表
            page_number: 页面编号
            
        Returns:
            PageLayout: 页面布局信息
        """
        self.logger.info(f"创建第{page_number}页布局")
        
        # 计算分镜布局
        panel_layouts = self._calculate_panel_layouts(len(images))
        
        # 组合分镜和图像数据
        panels = []
        for i, (panel_layout, image_data) in enumerate(zip(panel_layouts, images)):
            panel_info = {
                'panel_number': i + 1,
                'layout': panel_layout,
                'image_data': image_data,
                'script': page_script.panels[i] if i < len(page_script.panels) else None
            }
            panels.append(panel_info)
        
        return PageLayout(
            page_number=page_number,
            width=self.page_width,
            height=self.page_height,
            panels=panels,
            metadata={
                'page_summary': page_script.page_summary,
                'layout_type': self._get_layout_type(len(images))
            }
        )
    
    def _calculate_panel_layouts(self, panel_count: int) -> List[PanelLayout]:
        """计算分镜布局位置"""
        layouts = []
        
        # 可用区域
        available_width = self.page_width - 2 * self.margin
        available_height = self.page_height - 2 * self.margin
        
        if panel_count == 1:
            # 单分镜：占满整页
            layouts.append(PanelLayout(
                x=self.margin,
                y=self.margin,
                width=available_width,
                height=available_height,
                panel_number=1
            ))
        elif panel_count == 2:
            # 两分镜：上下布局
            panel_height = (available_height - self.panel_spacing) // 2
            layouts.extend([
                PanelLayout(
                    x=self.margin,
                    y=self.margin,
                    width=available_width,
                    height=panel_height,
                    panel_number=1
                ),
                PanelLayout(
                    x=self.margin,
                    y=self.margin + panel_height + self.panel_spacing,
                    width=available_width,
                    height=panel_height,
                    panel_number=2
                )
            ])
        elif panel_count == 3:
            # 三分镜：上一下二布局
            top_height = available_height // 2
            bottom_height = available_height - top_height - self.panel_spacing
            bottom_width = (available_width - self.panel_spacing) // 2
            
            layouts.extend([
                PanelLayout(
                    x=self.margin,
                    y=self.margin,
                    width=available_width,
                    height=top_height,
                    panel_number=1
                ),
                PanelLayout(
                    x=self.margin,
                    y=self.margin + top_height + self.panel_spacing,
                    width=bottom_width,
                    height=bottom_height,
                    panel_number=2
                ),
                PanelLayout(
                    x=self.margin + bottom_width + self.panel_spacing,
                    y=self.margin + top_height + self.panel_spacing,
                    width=bottom_width,
                    height=bottom_height,
                    panel_number=3
                )
            ])
        else:  # 4个或更多分镜：2x2网格
            panel_width = (available_width - self.panel_spacing) // 2
            panel_height = (available_height - self.panel_spacing) // 2
            
            positions = [
                (0, 0), (1, 0), (0, 1), (1, 1)
            ]
            
            for i in range(min(panel_count, 4)):
                col, row = positions[i]
                layouts.append(PanelLayout(
                    x=self.margin + col * (panel_width + self.panel_spacing),
                    y=self.margin + row * (panel_height + self.panel_spacing),
                    width=panel_width,
                    height=panel_height,
                    panel_number=i + 1
                ))
        
        return layouts
    
    def _get_layout_type(self, panel_count: int) -> str:
        """获取布局类型名称"""
        layout_types = {
            1: "single_panel",
            2: "vertical_split",
            3: "top_one_bottom_two",
            4: "grid_2x2"
        }
        return layout_types.get(panel_count, "grid_2x2")
    
    def render_page_to_image(self, page_layout: PageLayout) -> str:
        """
        将页面渲染为图像
        
        Args:
            page_layout: 页面布局
            
        Returns:
            渲染后的图像路径
        """
        if not Image:
            raise ImportError("请安装Pillow库: pip install Pillow")
        
        # 创建空白页面
        page_image = Image.new('RGB', (page_layout.width, page_layout.height), 'white')
        draw = ImageDraw.Draw(page_image)
        
        # 渲染每个分镜
        for panel_info in page_layout.panels:
            self._render_panel(page_image, draw, panel_info)
        
        # 保存图像
        output_dir = Path("output/pages")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_path = output_dir / f"page_{page_layout.page_number:03d}.png"
        page_image.save(output_path, 'PNG', dpi=(300, 300))
        
        self.logger.info(f"页面已渲染: {output_path}")
        return str(output_path)
    
    def _render_panel(self, page_image: Image.Image, draw: ImageDraw.Draw, panel_info: Dict[str, Any]) -> None:
        """渲染单个分镜"""
        layout = panel_info['layout']
        image_data = panel_info['image_data']
        script = panel_info.get('script')
        
        # 绘制分镜边框
        draw.rectangle([
            layout.x, layout.y,
            layout.x + layout.width, layout.y + layout.height
        ], outline='black', width=2)
        
        # 加载并缩放分镜图像
        if image_data.get('image_path') and Path(image_data['image_path']).exists():
            try:
                panel_image = Image.open(image_data['image_path'])
                panel_image = panel_image.resize((layout.width - 4, layout.height - 60), Image.Resampling.LANCZOS)
                page_image.paste(panel_image, (layout.x + 2, layout.y + 2))
            except Exception as e:
                self.logger.error(f"加载分镜图像失败: {e}")
        
        # 添加对话框
        if script and script.dialogue:
            self._add_dialogue(draw, layout, script.dialogue)
    
    def _add_dialogue(self, draw: ImageDraw.Draw, layout: PanelLayout, dialogue: List[str]) -> None:
        """添加对话框"""
        if not dialogue:
            return
        
        # 简单的对话框实现
        dialogue_text = " ".join(dialogue)
        
        # 对话框位置（分镜底部）
        text_y = layout.y + layout.height - 50
        text_x = layout.x + 10
        
        # 绘制对话框背景
        draw.rectangle([
            text_x - 5, text_y - 5,
            layout.x + layout.width - 10, layout.y + layout.height - 5
        ], fill='white', outline='black')
        
        # 绘制文字
        try:
            font = ImageFont.truetype(self.font_name, self.font_size)
        except:
            font = ImageFont.load_default()
        
        draw.text((text_x, text_y), dialogue_text, fill='black', font=font)
