#!/usr/bin/env python3
"""
AI Comic Generator 使用示例
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.comic_generator import ComicGenerator


def example_basic_comic():
    """基础漫画生成示例"""
    print("🎨 基础漫画生成示例")
    
    # 初始化生成器
    generator = ComicGenerator("config/config.yaml")
    
    # 生成漫画
    comic = generator.generate_comic(
        prompt="一个小机器人在未来城市中寻找朋友的温馨故事",
        pages=3,
        style="日式漫画",
        genre="科幻"
    )
    
    print(f"✅ 生成完成: {comic.title}")
    print(f"📄 页数: {len(comic.pages)}")
    
    # 保存漫画
    comic.save("examples/output/basic_comic.pdf")
    print("💾 已保存到: examples/output/basic_comic.pdf")


def example_character_sheet():
    """角色设定图生成示例"""
    print("\n👤 角色设定图生成示例")
    
    generator = ComicGenerator("config/config.yaml")
    
    # 生成角色设定图
    character_sheet = generator.generate_character_sheet(
        character_description="一个可爱的小机器人，蓝色外壳，大眼睛，友善的表情",
        style="日式漫画",
        poses=["正面", "侧面", "开心表情", "思考表情"]
    )
    
    print("✅ 角色设定图生成完成")
    for image_info in character_sheet['images']:
        if image_info.get('image_path'):
            print(f"📸 {image_info['pose']}: {image_info['image_path']}")


def example_custom_style():
    """自定义风格示例"""
    print("\n🎭 自定义风格示例")
    
    generator = ComicGenerator("config/config.yaml")
    
    # 生成不同风格的漫画
    styles = ["日式漫画", "美式漫画", "水彩风格"]
    
    for style in styles:
        print(f"生成 {style} 风格...")
        comic = generator.generate_comic(
            prompt="勇敢的冒险者探索神秘森林",
            pages=2,
            style=style,
            genre="奇幻"
        )
        
        output_path = f"examples/output/{style}_comic.pdf"
        comic.save(output_path)
        print(f"✅ {style} 完成: {output_path}")


def example_batch_generation():
    """批量生成示例"""
    print("\n📚 批量生成示例")
    
    generator = ComicGenerator("config/config.yaml")
    
    # 多个故事提示
    prompts = [
        "太空探险队发现神秘星球",
        "魔法学院的新生冒险",
        "机器人与人类的友谊故事"
    ]
    
    for i, prompt in enumerate(prompts, 1):
        print(f"生成第 {i} 个故事...")
        comic = generator.generate_comic(
            prompt=prompt,
            pages=2,
            style="日式漫画"
        )
        
        output_path = f"examples/output/batch_comic_{i}.pdf"
        comic.save(output_path)
        print(f"✅ 完成: {comic.title}")


def main():
    """运行所有示例"""
    print("🚀 AI Comic Generator 使用示例")
    print("=" * 50)
    
    # 创建输出目录
    Path("examples/output").mkdir(parents=True, exist_ok=True)
    
    try:
        # 运行示例
        example_basic_comic()
        example_character_sheet()
        example_custom_style()
        example_batch_generation()
        
        print("\n🎉 所有示例运行完成!")
        print("📁 查看生成结果: examples/output/")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {str(e)}")
        print("💡 请确保:")
        print("1. 已正确配置API密钥")
        print("2. 已安装所有依赖")
        print("3. 网络连接正常")


if __name__ == "__main__":
    main()
